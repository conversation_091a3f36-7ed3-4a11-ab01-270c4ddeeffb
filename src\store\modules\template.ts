import type { DjInfo } from '#/store';

import { defineStore } from 'pinia';
import { store } from '@/store';
import { h } from 'vue';
// import { Persistent } from '@/utils/cache/persistent';
// import { deepMerge } from '@/utils';
// import { get } from 'sortablejs';
import {
  getGzDetailApi,
  deleteGzApi,
  addOrUpdateGzApi,
  getInitValueApi,
  saveDocumentApi,
  chkFldApi,
  extractDetailApi,
  aofCheckDjApi,
} from '@/api/template/index';
import { extractRawApi } from '@/api/extractRawApi';

import { Modal } from 'ant-design-vue';
import nzhcn from 'nzh/cn';
import { CurrencyFormatter } from '@/utils/currency';

interface TemplateState {
  djInfo: Nullable<DjInfo>;
  templateForm: any;
  gzbh: string;
  djlx: string;
  djbs: string;
  danjbh: string;
  djSaved: boolean;
  resetForm: boolean;
  lastSavePromise: any;
  lastSubmitDataHash: any;
  funcinfo: any;
  onlyEditField: any;
  tempExpContent: any;
  firstLoad: boolean;
  allGzbhList: string[];
}

export const useTemplateStore = defineStore({
  id: 'template',
  state: (): TemplateState => ({
    djInfo: null,
    templateForm: {
      kbxtable: {},
      mxtable: [],
    },
    gzbh: '',
    djlx: '',
    djbs: '',
    danjbh: '',
    djSaved: false,
    resetForm: false,
    lastSavePromise: null,
    lastSubmitDataHash: null,
    funcinfo: {},
    onlyEditField: {},
    tempExpContent: {},
    firstLoad: true,
    allGzbhList: [],
  }),
  getters: {
    getDjInfo(state): DjInfo | null {
      return state.djInfo;
    },
    getTemplateForm(state): any {
      return state.templateForm;
    },
    getAllGzbhList(state): string[] {
      return state.allGzbhList;
    },
  },
  actions: {
    setDjInfo(djInfo: DjInfo) {
      this.djInfo = djInfo;
    },
    setDanjbh(danjbh: string) {
      this.danjbh = danjbh;
    },
    setTempExpContent(tempExpContent: any) {
      this.tempExpContent = tempExpContent;
    },
    setTemplateForm(templateForm: any) {
      // 确保templateForm不为null或undefined
      if (!templateForm || typeof templateForm !== 'object') {
        console.warn('[templateStore] setTemplateForm received invalid data, using default');
        templateForm = {
          kbxtable: {},
          mxtable: [{}],
        };
      }

      // 确保必要的属性存在
      if (!templateForm.kbxtable) {
        templateForm.kbxtable = {};
      }
      if (!templateForm.mxtable) {
        templateForm.mxtable = [{}];
      }

      this.templateForm = templateForm;
      this.lastSubmitDataHash = this.calculateMxtableHash(templateForm.mxtable);
    },
    setDjbs(djbs: string) {
      this.djbs = djbs;
    },
    setGzInfo(data: any) {
      const { gzbh, djlx, djbs, templateForm, resetForm } = data;

      // 如果djlx发生变化，清理相关状态
      if (this.djlx && this.djlx !== djlx) {
        this.clearStateForNewDjlx();
      }

      this.setGzbh(gzbh); // 使用 setGzbh 方法来记录 gzbh
      this.djlx = djlx;
      this.djbs = djbs;
      this.resetForm = resetForm;
      this.templateForm = templateForm;
    },
    setFuncInfo(funcinfo: any) {
      this.funcinfo = funcinfo;
    },
    setGzbh(gzbh: string | null) {
      this.gzbh = gzbh || '';
      // 如果 gzbh 不为空且不在列表中，则添加到列表
      if (gzbh && gzbh.trim() !== '' && !this.allGzbhList.includes(gzbh)) {
        this.allGzbhList.push(gzbh);
      }
    },
    // 获取所有gzbh列表
    getAllGzbh(): string[] {
      return this.allGzbhList.filter((gzbh) => gzbh && gzbh.trim() !== '');
    },
    // 清空所有gzbh列表
    clearAllGzbh(): void {
      this.allGzbhList = [];
    },
    // 从列表中移除指定的gzbh
    removeGzbh(gzbh: string): void {
      const index = this.allGzbhList.indexOf(gzbh);
      if (index > -1) {
        this.allGzbhList.splice(index, 1);
      }
    },
    setDjSaved(djSaved: boolean) {
      this.djSaved = djSaved;
    },
    setAllNull() {
      this.templateForm = {
        kbxtable: {},
        mxtable: [],
      };
      this.gzbh = '';
      this.djlx = '';
      this.djbs = '';
      this.lastSubmitDataHash = null;
      this.allGzbhList = []; // 清空所有 gzbh 列表
    },
    setResetForm(resetForm: boolean) {
      this.resetForm = resetForm;
    },
    /**
     * 检查对象中除了指定字段外，其他字段是否都是空值（空字符串、0、null、undefined）
     * @param obj 要检查的对象
     * @param exceptFields 排除检查的字段列表
     * @returns 如果除了指定字段外，其他字段都是空值，则返回true
     */
    checkIfAllFieldsEmptyExcept(obj: Record<string, any>, exceptFields: string[]): boolean {
      // 如果对象为空，返回true
      if (!obj || Object.keys(obj).length === 0) {
        return true;
      }

      // 检查对象中所有字段
      for (const key in obj) {
        // 跳过排除的字段
        if (exceptFields.includes(key)) {
          continue;
        }

        const value = obj[key];
        // 检查值是否为空（不是空字符串、0、null、undefined）
        if (
          value !== '' &&
          value !== 0 &&
          value !== '0' &&
          value !== null &&
          value !== undefined &&
          !(typeof value === 'string' && value.trim() === '')
        ) {
          return false; // 找到一个非空值，返回false
        }
      }

      return true; // 所有检查的字段都是空值
    },
    async fetchTemplateForm(gzbh?: string, bizId?: string, djlx?: string, djbs?: string) {
      // 生成存储键，使用djlx和djbs确保唯一性
      const currentDjlx = djlx || this.djlx;
      const currentDjbs = djbs || this.djbs;
      const storageKey = `gz_${currentDjlx}_${currentDjbs}`;

      // 获取现有的挂账数据
      const existingDataStr = localStorage.getItem('gz_data') || '{}';
      const existingData = JSON.parse(existingDataStr);

      // 检查缓存数据
      const cachedData = existingData[storageKey]?.data;
      if (cachedData && cachedData.gzbh === gzbh && cachedData.djlx === currentDjlx) {
        // 如果缓存中的gzbh和djlx都一致，直接返回缓存中的值
        this.setTemplateForm(cachedData);
        return this.templateForm;
      }

      // 根据参数情况调用不同的API查询方式
      let data;
      if (gzbh) {
        // 场景1：当gzbh存在时，通过gzbh查询
        data = await getGzDetailApi({ gzbh, bizId });
      } else if (currentDjlx && currentDjbs) {
        // 场景2：查询是否存在历史挂账，通过djlx和djbs查询
        data = await getGzDetailApi({ djlx: currentDjlx, djbs: currentDjbs });
      } else {
        Modal.error({
          title: '参数错误',
          content: '需要提供 gzbh 或者 djlx+djbs 参数',
          okText: '确定',
        });
        return;
      }

      // 优先使用后端返回的 gzbh 数据，如果后端返回的 gzbh 有值，则使用后端的数据覆盖本地的 gzbh
      if (data && data.gzbh) {
        console.log(
          `[fetchTemplateForm] 使用后端返回的 gzbh: ${data.gzbh}，覆盖本地 gzbh: ${this.gzbh}`,
        );
        this.setGzbh(data.gzbh);
      } else if (gzbh) {
        // 如果后端没有返回 gzbh，但传入了 gzbh 参数，则使用传入的 gzbh
        console.log(`[fetchTemplateForm] 后端未返回 gzbh，使用传入的 gzbh: ${gzbh}`);
        this.setGzbh(gzbh);
      }

      // 处理 kbxtable 数据，排除特定字段以保护现有值
      const currentKbxtable = this.templateForm?.kbxtable || {};
      const newKbxtable = { ...currentKbxtable };

      // 遍历新数据，排除特定字段
      if (data.kbxtable) {
        console.log('[fetchTemplateForm] 后端返回的 kbxtable 数据:', data.kbxtable);
        console.log('[fetchTemplateForm] 后端返回的 danjbh:', data.kbxtable.danjbh);
        console.log('[fetchTemplateForm] 后端返回的 caozy:', data.kbxtable.caozy);

        Object.keys(data.kbxtable).forEach((key: string) => {
          // 排除内部字段和特定的时间字段（ontime、riqi）
          // 保留 danjbh 和 caozy 字段，确保后端返回的值能正确设置
          if (key !== '_X_ROW_KEY' && key !== 'ontime' && key !== 'riqi') {
            newKbxtable[key] = (data.kbxtable as any)[key];
            if (key === 'danjbh' || key === 'caozy') {
              console.log(`[fetchTemplateForm] 设置重要字段 ${key}:`, (data.kbxtable as any)[key]);
            }
          }
        });
      }

      this.setTemplateForm({
        ...data,
        kbxtable: newKbxtable,
      });
    },
    async initGz({
      djlx,
      djbs,
      kbxTableData,
      mxTableDataList,
    }: {
      djlx: string;
      djbs: string;
      kbxTableData: any;
      mxTableDataList: any[];
    }) {
      this.setDjSaved(false);

      // 检查localStorage中是否有缓存数据
      const storageKey = `gz_${djlx}`;
      const existingDataStr = localStorage.getItem('gz_data') || '{}';
      const existingData = JSON.parse(existingDataStr);
      const cachedData = existingData[storageKey]?.data;

      // 无论是否有缓存数据，都获取初始值
      const initData = await getInitValueApi(djlx, 0);
      if (cachedData && cachedData.djlx === djlx) {
        // 有缓存数据且djlx一致，使用缓存数据但用初始值覆盖时间相关字段
        this.setGzInfo({
          djlx,
          djbs,
          gzbh: cachedData.gzbh,
          templateForm: {
            kbxtable:
              {
                ...cachedData.kbxtable,
                riqi: initData.riqi,
                ontime: initData.ontime,
                caozy: initData.caozy,
                danjbh: initData.danjbh,
                djbhbs: initData.djbhbs,
                duifbsh: initData.duifbsh,
              } ?? {},
            mxtable: cachedData.mxtable ?? [],
          },
        });
      } else {
        // 无缓存数据，使用初始值并为数值型字段设置默认值
        kbxTableData = { ...kbxTableData, ...initData };

        // 汇总项也不设置默认值，保持原值，只在接口调用时转换

        // 明细表数据不设置默认值，保持原值
        const processedMxTableDataList = mxTableDataList;

        this.setGzInfo({
          djlx,
          djbs,
          gzbh: '',
          templateForm: {
            kbxtable: kbxTableData ?? {},
            mxtable: processedMxTableDataList ?? [],
          },
        });
      }

      // 保存到localStorage
      await this.saveGz();

      // // 调用后端保存
      // await this.saveGzToServer(storageKey, false);
    },
    async clearGz(gzbhlist: string[], bizId: string) {
      if (!gzbhlist || gzbhlist.length <= 0 || gzbhlist[0]?.trim() === '') return;

      // 删除工作表
      await deleteGzApi({ gzbhlist, bizId });

      // 从全局 gzbh 列表中移除已清除的 gzbh
      gzbhlist.forEach((gzbh) => {
        if (gzbh && gzbh.trim() !== '') {
          this.removeGzbh(gzbh);
        }
      });

      // 删除成功后，判断当前gzbh是否在删除列表中，如果在则清空表单
      if (gzbhlist.includes(this.gzbh)) {
        // 清除localStorage中的缓存
        const storageKey = `gz_${this.djlx}_${this.djbs}`;
        const existingDataStr = localStorage.getItem('gz_data') || '{}';
        const existingData = JSON.parse(existingDataStr);
        delete existingData[storageKey];
        localStorage.setItem('gz_data', JSON.stringify(existingData));

        this.setGzInfo({
          djlx: null,
          djbs: null,
          gzbh: null,
          templateForm: {
            kbxtable: {},
            mxtable: [],
          },
        });
      }
    },

    async clearGzInfo(gzbhlist: string[], bizId: string = '') {
      if (!gzbhlist || gzbhlist.length <= 0 || gzbhlist[0]?.trim() === '') return;
      // 删除工作表
      await deleteGzApi({ gzbhlist, bizId });
    },

    // 添加计算mxtable哈希值的方法
    calculateMxtableHash(mxtable: any[]) {
      return JSON.stringify(mxtable);
    },

    /**
     * 转换P类型字段的中文数字为阿拉伯数字
     * 对于lx="P"的字段，将其中文大写金额转换为对应的阿拉伯数字
     * 只有当存在相同fieldName的lx="S"字段时才进行转换
     * @param kbxtable 表头数据
     * @returns 转换后的表头数据
     */
    convertChineseNumbersForP(kbxtable: any): any {
      if (!kbxtable || !this.djInfo?.kbxTableList) {
        return kbxtable;
      }

      const processedKbxtable = { ...kbxtable };

      // 先构建所有lx="S"字段的fieldName集合，用于快速查找
      const sFieldNames = new Set<string>();
      this.djInfo.kbxTableList.forEach((fieldConfig) => {
        if (fieldConfig.lx === 'S' && fieldConfig.fieldName) {
          sFieldNames.add(fieldConfig.fieldName);
        }
      });

      // 遍历所有字段配置，查找lx="P"的字段
      this.djInfo.kbxTableList.forEach((fieldConfig) => {
        if (fieldConfig.lx === 'P' && fieldConfig.fieldName) {
          const fieldName = fieldConfig.fieldName;
          const fieldValue = processedKbxtable[fieldName];

          // 检查是否存在相同fieldName的lx="S"字段
          if (!sFieldNames.has(fieldName)) {
            console.log(`[数据转换] 跳过lx="P"字段 ${fieldName}，因为不存在对应的lx="S"字段`);
            return; // 跳过该P字段的处理
          }

          // 如果字段值存在且为字符串类型（中文数字）
          if (fieldValue && typeof fieldValue === 'string' && fieldValue.trim() !== '') {
            try {
              // 获取对应S字段的小数位数配置
              const sFieldConfig = this.djInfo?.kbxTableList?.find(
                (config) => config.lx === 'S' && config.fieldName === fieldName,
              );
              const decimals = sFieldConfig?.fldDec !== undefined ? sFieldConfig.fldDec : 2;

              // 使用统一的金额格式化工具进行转换
              const arabicNumber = CurrencyFormatter.fromChineseUppercase(fieldValue, decimals);

              // 如果转换成功且结果为有效数字
              if (typeof arabicNumber === 'number' && !isNaN(arabicNumber)) {
                processedKbxtable[fieldName] = arabicNumber;
                console.log(
                  `[数据转换] lx="P"字段 ${fieldName}: "${fieldValue}" -> ${arabicNumber} (小数位数: ${decimals})`,
                );
              } else {
                console.warn(
                  `[数据转换] lx="P"字段 ${fieldName} 转换失败，保持原值: "${fieldValue}"`,
                );
              }
            } catch (error) {
              console.error(
                `[数据转换] lx="P"字段 ${fieldName} 转换出错:`,
                error,
                '原值:',
                fieldValue,
              );
              // 转换失败时保持原值不变
            }
          }
        }
      });

      return processedKbxtable;
    },

    /**
     * 保存挂账数据到localStorage
     */
    async saveGz() {
      const { gzbh, djlx, djbs } = this;

      if (!djlx || !djbs) {
        console.log('单据类型或单据标识不存在');
        return;
      }

      const { kbxtable, mxtable } = this.templateForm;
      const currentMxtableHash = this.calculateMxtableHash(mxtable);

      // 过滤数据，排除lx="P"的字段
      const filteredKbxtable = this.convertChineseNumbersForP(kbxtable);

      const submitData = { gzbh, djlx, djbs, kbxtable: filteredKbxtable, mxtable };
      // 如果mxtable没有变化，直接返回
      if (this.lastSubmitDataHash != null && currentMxtableHash != this.lastSubmitDataHash) {
        // 校验提交数据格式是否正确，非手动编辑
        if (!this.checkSubmitData(submitData, true, false)) {
          return;
        }
      }
      if (mxtable && mxtable.length > 0) {
        submitData.mxtable = mxtable?.map((item: any, index: number) => {
          return { ...item, dj_rec: index + 1 };
        });
        // 剔除tableData中的_X_ROW_KEY字段值
        submitData.mxtable = submitData.mxtable.map((item) => {
          const { _X_ROW_KEY, ...rest } = item;
          return rest;
        });
      }

      try {
        // 使用djlx_djbs作为key
        const storageKey = `gz_${djlx}`;

        // 获取现有的挂账数据
        const existingDataStr = localStorage.getItem('gz_data') || '{}';
        const existingData = JSON.parse(existingDataStr);

        // 检查除了ontime和riqi字段外，其他字段是否都是空值
        const isEmpty = this.checkIfAllFieldsEmptyExcept(kbxtable, ['ontime', 'riqi']);

        // 只有在非空的情况下才更新数据
        if (!isEmpty || !existingData[storageKey]) {
          // 更新数据
          existingData[storageKey] = {
            data: submitData,
            timestamp: new Date().getTime(),
          };
          console.log('更新localStorage数据', existingData[storageKey]);
        } else {
          console.log('表单数据为空，不更新localStorage');
        }

        // 保存回localStorage
        localStorage.setItem('gz_data', JSON.stringify(existingData));

        // 更新最后一次mxtable的哈希值
        this.lastSubmitDataHash = currentMxtableHash;

        return { success: true, storageKey };
      } catch (error) {
        console.error('保存到localStorage失败:', error);
        throw error;
      }
    },

    /**
     * 从localStorage读取数据并保存到服务器
     * @param storageKey 可选,指定要保存的单据key,不传则保存所有
     */
    async saveGzToServer(storageKey: string, _isManualGz: boolean = false, beizhu = '') {
      // 步骤1: 在保存前先进行字段校验
      const validationResult = await this.validateFieldsBeforeSave();
      if (!validationResult.valid) {
        Modal.warn({
          title: '字段校验失败',
          content: h('div', {
            innerHTML: validationResult.errors
              .map((error) => error.replace(/\r/g, '<br/>'))
              .join('<br/>'),
          }),
          okText: '确定',
        });
        return;
      }

      // 在保存前确保所有汇总项都被计算
      this.calculateAllSummaryItems();

      // 强制重新计算所有表达式字段，确保传给后端的数据是最新计算结果
      this.forceRecalculateAllExpressions();

      // 在保存前处理数值型字段，将空值转为0
      if (this.djInfo?.kbxTableList && this.templateForm.kbxtable) {
        this.djInfo.kbxTableList.forEach((item) => {
          if (
            item.fieldName &&
            (item.fldType === '1' || item.fldType === '2') &&
            (this.templateForm.kbxtable[item.fieldName] === '' ||
              this.templateForm.kbxtable[item.fieldName] === null ||
              this.templateForm.kbxtable[item.fieldName] === undefined)
          ) {
            this.templateForm.kbxtable[item.fieldName] = 0;
          }
        });
      }

      if (this.djInfo?.mxTableList && this.templateForm.mxtable) {
        this.templateForm.mxtable = this.templateForm.mxtable.map((row) => {
          const newRow = { ...row };
          this.djInfo!.mxTableList!.forEach((item) => {
            // 如果是表达式字段，不修改其值（保持表达式计算结果）
            if (item.expContent && item.expContent.trim() !== '') {
              return;
            }

            if ((item.fldType === '1' || item.fldType === '2') && item.fieldName) {
              // 处理空值
              if (
                newRow[item.fieldName] === '' ||
                newRow[item.fieldName] === null ||
                newRow[item.fieldName] === undefined
              ) {
                newRow[item.fieldName] = 0;
              } else {
                // 对数值型字段应用精度格式化
                const numValue = Number(newRow[item.fieldName]);
                if (!isNaN(numValue)) {
                  newRow[item.fieldName] = this.formatNumberWithPrecision(
                    numValue,
                    item.fldDec || 2,
                  );
                }
              }
            }
          });
          return newRow;
        });
      }
      try {
        // 清理 mxtable 数据，移除所有行的前端UI状态字段
        const cleanedMxtable = this.templateForm.mxtable.map((row, index) => {
          // 定义需要过滤的前端UI状态字段
          const uiStateFields = [
            '_X_ROW_KEY',
            'isCurrent',
            'iscurrent',
            '_selected',
            '_checked',
            '_expanded',
          ];

          // 记录被过滤的字段（用于调试）
          const filteredFields = Object.keys(row).filter((key) => uiStateFields.includes(key));
          if (filteredFields.length > 0) {
            console.log(`[挂账数据清理] 第${index + 1}行过滤的UI状态字段:`, filteredFields);
          }

          // 创建清理后的行数据
          const cleanedRow = {};
          Object.keys(row).forEach((key) => {
            // 只保留非UI状态字段
            if (!uiStateFields.includes(key)) {
              cleanedRow[key] = row[key];
            }
          });

          return cleanedRow;
        });

        // 过滤数据，排除lx="P"的字段，并清理UI状态字段
        const convertedKbxtable = this.convertChineseNumbersForP(this.templateForm.kbxtable);

        // 清理 kbxtable 数据，移除前端UI状态字段
        const uiStateFields = ['isCurrent', 'iscurrent', '_selected', '_checked', '_expanded'];

        // 记录被过滤的字段（用于调试）
        const filteredKbxFields = Object.keys(convertedKbxtable).filter((key) =>
          uiStateFields.includes(key),
        );
        if (filteredKbxFields.length > 0) {
          console.log('[挂账数据清理] kbxtable过滤的UI状态字段:', filteredKbxFields);
        }

        const filteredKbxtable = {};
        Object.keys(convertedKbxtable).forEach((key) => {
          // 只保留非UI状态字段
          if (!uiStateFields.includes(key)) {
            filteredKbxtable[key] = convertedKbxtable[key];
          }
        });

        const dataToSave: any = {
          gzbh: this.gzbh,
          djlx: this.djlx,
          djbs: this.djbs,
          kbxtable: filteredKbxtable,
          mxtable: cleanedMxtable,
        };

        if (beizhu !== undefined) {
          dataToSave.beizhu = beizhu;
        }

        const result = await addOrUpdateGzApi(dataToSave);
        this.setGzbh(result.gzbh);
        return { success: true, gzbh: result.gzbh };
      } catch (error) {
        console.error('保存到服务器失败:', error);
        throw error;
      }
    },

    /**
     * 在保存到服务器前进行字段校验
     * @returns Promise<{valid: boolean, errors: string[]}>
     */
    async validateFieldsBeforeSave(): Promise<{ valid: boolean; errors: string[] }> {
      const { kbxtable, mxtable } = this.templateForm;
      const { kbxTableList, mxTableList } = this.djInfo || {};

      if (!kbxTableList || !mxTableList) {
        console.warn('djInfo中缺少必要的表格配置信息');
        return { valid: true, errors: [] };
      }

      const errors: string[] = [];
      let validResult = true;

      // 校验表头数据 - 基础数据类型校验
      if (kbxtable) {
        const kbxTableListKeys = Object.keys(kbxTableList);
        kbxTableListKeys.forEach((key) => {
          if (kbxTableList[key].fldType == 1 || kbxTableList[key].fldType == 2) {
            const fieldName = kbxTableList[key].fieldName;
            if (kbxtable[fieldName] && isNaN(kbxtable[fieldName])) {
              errors.push(`${kbxTableList[key].fieldTitle}必须是数值类型`);
              validResult = false;
            }
          }
        });
      }

      // 校验明细表数据
      if (mxtable && mxtable.length > 0) {
        // 收集需要校验的字段信息
        const validateFields: Array<{
          gzbh: string;
          djlxbs: string;
          fldname: string;
          fldvalue: any;
          rowIndex: number;
          fieldTitle: string;
          dj_rec: number;
          dspid: string;
          dkfid: string;
          jwh: string;
          miejph: string;
          hescbj: string;
          hsgscb: string;
          picih: string;
          ddwid: string;
          is_zzs: string;
          beizhu: string;
          beizhu1: string;
          beizhu2: string;
          xgdj_rec: string;
          yuandjbh: string;
          xgdjbh: string;
          fus: string;
        }> = [];

        for (let index = 0; index < mxtable.length; index++) {
          const item = mxtable[index];

          for (const key of Object.keys(mxTableList)) {
            // 数值类型校验
            if (mxTableList[key].fldType == 1 || mxTableList[key].fldType == 2) {
              const fieldName = mxTableList[key].fieldName;
              if (item[fieldName] && isNaN(item[fieldName])) {
                errors.push(`第${index + 1}行【${mxTableList[key].fieldTitle}】必须是数值类型`);
                validResult = false;
              }
            }

            // 收集需要字段校验的数据 - 只校验标记为需要校验的字段
            if ((mxTableList[key] as any).ischkfld) {
              const fieldName = mxTableList[key].fieldName;
              let value = item[fieldName];

              // 如果是数值型字段且为空值，转换为0
              if (
                (mxTableList[key].fldType === '1' || mxTableList[key].fldType === '2') &&
                (value === '' || value === null || value === undefined)
              ) {
                value = 0;
              }

              if (this.djlx && typeof value !== 'undefined' && value != null && fieldName) {
                validateFields.push({
                  gzbh: this.gzbh,
                  djlxbs: this.djlx,
                  fldname: fieldName,
                  fldvalue: value,
                  rowIndex: index,
                  fieldTitle: mxTableList[key].fieldTitle,
                  dj_rec: index + 1,
                  dspid: item.dspid || '',
                  dkfid: item.dkfid || '',
                  jwh: item.jwh || '',
                  miejph: item.miejph || '',
                  hescbj: item.hescbj || '',
                  hsgscb: item.hsgscb || '',
                  picih: item.picih || '',
                  ddwid: item.ddwid || this.templateForm.kbxtable.ddwid || '',
                  is_zzs: item.is_zzs || '否',
                  beizhu: item.beizhu || '',
                  beizhu1: item.beizhu1 || '',
                  beizhu2: item.beizhu2 || '',
                  xgdj_rec: item.xgdj_rec || '',
                  yuandjbh: item.yuandjbh || '',
                  xgdjbh: item.xgdjbh || '',
                  fus: item.fus || '1',
                });
              }
            }
          }
        }

        // 如果有需要校验的字段，统一调用校验API
        if (validateFields.length > 0) {
          try {
            // 转换为API需要的格式
            const apiFields = validateFields.map((field) => ({
              gzbh: field.gzbh,
              djlxbs: field.djlxbs,
              fldname: field.fldname,
              fldvalue: field.fldvalue,
              dj_rec: field.dj_rec,
              dspid: field.dspid,
              dkfid: field.dkfid,
              jwh: field.jwh,
              miejph: field.miejph,
              hescbj: field.hescbj,
              hsgscb: field.hsgscb,
              picih: field.picih,
              ddwid: field.ddwid,
              is_zzs: field.is_zzs,
              beizhu: field.beizhu,
              beizhu1: field.beizhu1,
              beizhu2: field.beizhu2,
              xgdj_rec: field.xgdj_rec,
              yuandjbh: field.yuandjbh,
              xgdjbh: field.xgdjbh,
              fus: field.fus,
            }));

            const validateResults = await chkFldApi(apiFields as any);

            // 处理校验结果
            validateResults.forEach((result, index) => {
              const { pass, title } = result;
              if (pass == '-1' || pass == '1') {
                // 从validateFields中获取对应的行号和字段信息
                const validateField = validateFields[index];
                const rowIndex = validateField.rowIndex;
                const fieldTitle = validateField.fieldTitle;

                errors.push(
                  `第${rowIndex + 1}行【${fieldTitle}】: ${title == null ? '字段校验失败' : title}`,
                );
                validResult = false;
              }
            });
          } catch (error) {
            console.error('字段校验API调用失败:', error);
            errors.push('字段校验服务异常，请稍后重试');
            validResult = false;
          }
        }
      }

      return {
        valid: validResult,
        errors,
      };
    },

    /**
     * 获取localStorage中的所有挂账数据
     */
    getLocalGzData() {
      try {
        const existingDataStr = localStorage.getItem('gz_data') || '{}';
        return JSON.parse(existingDataStr);
      } catch (error) {
        console.error('获取localStorage数据失败:', error);
        return {};
      }
    },
    async saveDj(djEventType: number, bizId: string) {
      const { gzbh, djlx, djbs } = this;

      const { kbxtable, mxtable } = this.templateForm;

      // 过滤数据，排除lx="P"的字段
      const filteredKbxtable = this.convertChineseNumbersForP(kbxtable ?? {});

      const submitData = { gzbh, djlx, djbs, kbxtable: filteredKbxtable, mxtable: mxtable ?? [] };
      if (!this.checkRequired(submitData)) {
        return false;
      }
      const storageKey = `gz_${djlx}`;
      const existingDataStr = localStorage.getItem('gz_data') || '{}';
      const existingData = JSON.parse(existingDataStr);
      if (!this.djSaved) {
        if (djEventType === 2 || djEventType === 3) {
          await saveDocumentApi({
            gzbh: existingData[storageKey]?.data?.gzbh,
            is_xg: djEventType === 2,
            danjbh: this.danjbh,
            bizId,
          });
        } else {
          await saveDocumentApi({
            gzbh: existingData[storageKey]?.data?.gzbh,
            is_xg: bizId ? true : false,
            danjbh: '',
            bizId,
          });
        }

        // 清除缓存中的数据
        delete existingData[storageKey];
        localStorage.setItem('gz_data', JSON.stringify(existingData));

        // 清空store数据
        this.setAllNull();
        this.djSaved = true;
        return true;
      }
      this.djSaved = true;
    },
    async zlJs(funcname: string, params: Record<string, any>) {
      await extractRawApi({ funcname: funcname, params: params });
    },

    async getDjDetails(
      funcname: string,
      djEventType: number,
      params?: Record<string, any>,
      djKey?: string,
      djValue?: string,
    ) {
      const { djlx, djbs, danjbh } = this;
      const data = await extractDetailApi({
        djlx,
        djbs,
        danjbh,
        is_xg: djEventType === 2,
        funcname,
        params,
      });

      // 优先使用后端返回的 gzbh 数据
      if (data && data.gzbh) {
        console.log(
          `[getDjDetails] 使用后端返回的 gzbh: ${data.gzbh}，覆盖本地 gzbh: ${this.gzbh}`,
        );
        this.setGzbh(data.gzbh);
      }

      // 设置指定的字段值
      if (djKey) {
        data.kbxtable[djKey] = djValue;
      }

      // 处理 kbxtable 数据，排除特定字段以保护现有值
      const currentKbxtable = this.templateForm?.kbxtable || {};
      const newKbxtable = { ...currentKbxtable };

      // 遍历新数据，排除特定字段
      if (data.kbxtable) {
        Object.keys(data.kbxtable).forEach((key: string) => {
          // 排除内部字段和特定的时间字段（ontime、riqi）
          // 保留 danjbh 和 caozy 字段，确保后端返回的值能正确设置
          if (key !== '_X_ROW_KEY' && key !== 'ontime' && key !== 'riqi') {
            newKbxtable[key] = (data.kbxtable as any)[key];
          }
        });
      }

      this.setTemplateForm({
        ...data,
        kbxtable: newKbxtable,
      });
      this.saveGz();
      return data;
    },

    async checkSubmitData(
      data: any,
      showErrorModal: boolean = true,
      isManualEdit: boolean = true,
      validationMode: 'cell-switch' | 'cell-blur' = 'cell-switch',
    ) {
      if (this.firstLoad) {
        this.firstLoad = false;
        return true;
      }
      const { kbxtable, mxtable } = data;
      const { kbxTableList, mxTableList } = this.djInfo || {};
      if (!kbxTableList || !mxTableList) {
        console.warn('djInfo中缺少必要的表格配置信息');
        return true;
      }

      const kbxTableListKeys = Object.keys(kbxTableList);
      let validResult = true;
      const errors: string[] = []; // 收集所有错误信息

      // 校验表头数据
      if (kbxtable) {
        kbxTableListKeys.forEach((key) => {
          if (kbxTableList[key].fldType == 1 || kbxTableList[key].fldType == 2) {
            const fieldName = kbxTableList[key].fieldName;
            if (kbxtable[fieldName] && isNaN(kbxtable[fieldName])) {
              errors.push(`${kbxTableList[key].fieldTitle}必须是数值类型`);
              validResult = false;
            }
          }
        });
      }
      // 校验明细表数据
      if (mxtable && mxtable.length > 0) {
        // 定义校验字段的类型
        type ValidateFieldItem = {
          gzbh: string;
          djlxbs: string;
          fldname: string;
          fldvalue: any;
          rowIndex: number;
          fieldTitle: string;
          dj_rec: number;
          dspid: string;
          dkfid: string;
          jwh: string;
          miejph: string;
          hescbj: string;
          hsgscb: string;
          picih: string;
          ddwid: string;
          is_zzs: string;
          beizhu: string;
          beizhu1: string;
          beizhu2: string;
          xgdj_rec: string;
          yuandjbh: string;
          xgdjbh: string;
          fus: string;
          validationMode: 'cell-switch' | 'cell-blur';
        };

        // 收集需要校验的字段信息
        const validateFields: ValidateFieldItem[] = [];

        // 创建一个Map来存储字段信息,用于后续错误信息展示
        const fieldInfoMap = new Map();

        for (let index = 0; index < mxtable.length; index++) {
          const item = mxtable[index];
          for (const key of Object.keys(mxTableList)) {
            // 数值类型校验
            if (mxTableList[key].fldType == 1 || mxTableList[key].fldType == 2) {
              const fieldName = mxTableList[key].fieldName;
              if (item[fieldName] && isNaN(item[fieldName])) {
                errors.push(`第${index + 1}行【${mxTableList[key].fieldTitle}】必须是数值类型`);
                validResult = false;
              }
            }

            // 收集需要字段校验的数据 - 仅在手动编辑时才进行ischkfld字段校验
            console.log(mxTableList[key], (mxTableList[key] as any).ischkfld, isManualEdit);
            if ((mxTableList[key] as any).ischkfld && isManualEdit) {
              const fieldName = mxTableList[key].fieldName;
              let value = item[fieldName];

              // 如果是数值型字段且为空值，转换为0
              if (
                (mxTableList[key].fldType === '1' || mxTableList[key].fldType === '2') &&
                (value === '' || value === null || value === undefined)
              ) {
                value = 0;
              }

              if (this.djlx && typeof value !== 'undefined' && value != null && fieldName) {
                // 存储字段信息
                const fieldKey = `${index}_${fieldName}`;
                fieldInfoMap.set(fieldKey, {
                  rowIndex: index,
                  fieldTitle: mxTableList[key].fieldTitle,
                });
                validateFields.push({
                  gzbh: this.gzbh,
                  djlxbs: this.djlx,
                  fldname: fieldName,
                  fldvalue: value,
                  rowIndex: index,
                  fieldTitle: mxTableList[key].fieldTitle,
                  dj_rec: index + 1,
                  dspid: item.dspid || '',
                  dkfid: item.dkfid || '',
                  jwh: item.jwh || '',
                  miejph: item.miejph || '',
                  hescbj: item.hescbj || '',
                  hsgscb: item.hsgscb || '',
                  picih: item.picih || '',
                  ddwid: item.ddwid || this.templateForm.kbxtable.ddwid || '',
                  is_zzs: item.is_zzs || '否',
                  beizhu: item.beizhu || '',
                  beizhu1: item.beizhu1 || '',
                  beizhu2: item.beizhu2 || '',
                  xgdj_rec: item.xgdj_rec || '',
                  yuandjbh: item.yuandjbh || '',
                  xgdjbh: item.xgdjbh || '',
                  fus: item.fus || '1',
                  validationMode, // 添加验证模式
                });
              }
            }
          }
        }
        // 如果有需要校验的字段,统一调用校验API
        if (validateFields.length > 0) {
          // 转换为API需要的格式
          const apiFields = validateFields.map((field) => ({
            gzbh: field.gzbh,
            djlxbs: field.djlxbs,
            fldname: field.fldname,
            fldvalue: field.fldvalue,
            dj_rec: field.dj_rec,
            dspid: field.dspid,
            dkfid: field.dkfid,
            jwh: field.jwh,
            miejph: field.miejph,
            hescbj: field.hescbj,
            hsgscb: field.hsgscb,
            picih: field.picih,
            ddwid: field.ddwid,
            is_zzs: field.is_zzs,
            beizhu: field.beizhu,
            beizhu1: field.beizhu1,
            beizhu2: field.beizhu2,
            xgdj_rec: field.xgdj_rec,
            yuandjbh: field.yuandjbh,
            xgdjbh: field.xgdjbh,
            fus: field.fus,
          }));

          const validateResults = await chkFldApi(apiFields as any);
          // 处理校验结果
          validateResults.forEach((result, index) => {
            const { pass, title, fldname } = result;
            if (pass == '-1' || pass == '1') {
              // 从validateFields中获取对应的行号和字段信息
              const validateField = validateFields[index];
              const rowIndex = validateField.rowIndex;
              const fieldTitle = validateField.fieldTitle;

              // 根据验证模式决定是否清空字段值
              if (validationMode === 'cell-blur' && mxtable && mxtable[rowIndex] && fldname) {
                // 输入框失焦：根据字段类型清空值
                const fieldConfig = Object.values(mxTableList).find(
                  (config: any) => config.fieldName === fldname,
                );

                if (fieldConfig) {
                  if (fieldConfig.fldType === '1' || fieldConfig.fldType === '2') {
                    // 数值类型字段设置为 null
                    mxtable[rowIndex][fldname] = null;
                  } else {
                    // 字符串类型字段设置为空字符串
                    mxtable[rowIndex][fldname] = '';
                  }
                } else {
                  // 如果找不到字段配置，默认设置为空字符串
                  mxtable[rowIndex][fldname] = '';
                }
              }
              // 输入框间切换模式不清空字段值，让用户可以修改

              errors.push(
                `第${rowIndex + 1}行【${fieldTitle}】: ${title == null ? '错误' : title}`,
              );
              validResult = false;
            }
          });

          // 如果有字段值被清空，更新templateForm
          if (validResult === false) {
            this.setTemplateForm({ ...this.templateForm, mxtable });
          }
        }
      }
      // 如果有错误信息且需要显示弹窗,则显示错误弹窗
      if (errors.length > 0 && showErrorModal) {
        const modal = Modal.warn({
          title: '数据校验错误',
          content: h('div', {
            innerHTML: errors.map((error) => error.replace(/\r/g, '<br/>')).join('<br/>'),
          }),
          okText: '确定',
          keyboard: false, // 禁用默认的键盘行为
          maskClosable: false, // 禁止点击遮罩关闭
          onOk: () => {
            // 延迟一帧后重新聚焦到表格
            setTimeout(() => {
              const activeElement = document.activeElement as HTMLElement;
              if (activeElement) {
                activeElement.focus();
              }
            }, 0);
          },
        });

        // 添加自定义键盘事件处理
        const handleKeyDown = (e: KeyboardEvent) => {
          if (e.key === 'Enter') {
            modal.destroy();
            document.removeEventListener('keydown', handleKeyDown);
          }
        };
        document.addEventListener('keydown', handleKeyDown);
      }

      return validResult;
    },
    async validateField(data: {
      fieldName: string;
      fieldValue: any;
      rowIndex: number;
      mxtable: any[];
      dspid?: string;
      dkfid?: string;
      jwh?: string;
      miejph?: string;
      hescbj?: string;
      hsgscb?: string;
      picih?: string;
      ddwid?: string;
      dj_rec?: number;
      is_zzs?: string;
      beizhu?: string;
      beizhu1?: string;
      beizhu2?: string;
      xgdj_rec?: string;
      yuandjbh?: string;
      xgdjbh?: string;
      fus?: string;
      dzyid?: string;
      appver?: string;
      P_dfbs?: string;
      gjkl?: string;
      isManualEdit?: boolean;

      validationMode?: 'cell-switch' | 'cell-blur'; // 新增验证模式参数
    }) {
      const {
        fieldName,
        fieldValue,
        rowIndex,
        mxtable,
        dspid,
        dkfid,
        jwh,
        miejph,
        hescbj,
        hsgscb,
        picih,
        ddwid,
        dj_rec,
        is_zzs,
        beizhu,
        beizhu1,
        beizhu2,
        xgdj_rec,
        yuandjbh,
        xgdjbh,
        fus,
        isManualEdit = true,
        validationMode = 'cell-switch', // 默认为输入框间切换模式
      } = data;
      const { mxTableList } = this.djInfo || {};
      if (!mxTableList || !fieldName) return { valid: true };

      let validResult = true;
      const errors: string[] = [];
      let apiPass: string | undefined;
      let apiTitle: string | undefined;

      // 获取字段配置
      const fieldConfig = Object.values(mxTableList).find(
        (config: any) => config.fieldName === fieldName,
      );
      if (!fieldConfig) return { valid: true };

      // 数值类型校验
      if ((fieldConfig.fldType === '1' || fieldConfig.fldType === '2') && fieldValue) {
        if (isNaN(fieldValue)) {
          errors.push(`第${rowIndex + 1}行【${fieldConfig.fieldTitle}】必须是数值类型`);
          validResult = false;
        }
      }

      // 字段校验API - 仅在手动编辑且字段需要校验时调用
      if (
        (fieldConfig as any).ischkfld &&
        isManualEdit &&
        typeof fieldValue !== 'undefined' &&
        fieldValue != null
      ) {
        try {
          // 如果是数值型字段且为空值，转换为0
          let processedFieldValue = fieldValue;
          if (
            (fieldConfig.fldType === '1' || fieldConfig.fldType === '2') &&
            (fieldValue === '' || fieldValue === null || fieldValue === undefined)
          ) {
            processedFieldValue = 0;
          }

          const checkResult = await chkFldApi([
            {
              gzbh: this.gzbh,
              djlxbs: this.djlx,
              fldname: fieldName,
              fldvalue: processedFieldValue,
              dj_rec: dj_rec || rowIndex + 1,
              dspid: dspid || '',
              dkfid: dkfid || '',
              jwh: jwh || '',
              miejph: miejph || '',
              hescbj: hescbj || '',
              hsgscb: hsgscb || '',
              picih: picih || '',
              ddwid: ddwid || this.templateForm?.kbxtable?.ddwid || '',
              is_zzs: is_zzs || '否',
              beizhu: beizhu || '',
              beizhu1: beizhu1 || '',
              beizhu2: beizhu2 || '',
              xgdj_rec: xgdj_rec || '',
              yuandjbh: yuandjbh || '',
              xgdjbh: xgdjbh || '',
              fus: fus || '1',
            },
          ]);
          const { pass, title } = checkResult[0];
          apiPass = pass;
          apiTitle = title;

          if (pass == '-1' || pass == '1') {
            // 根据验证模式处理校验失败的情况
            if (validationMode === 'cell-switch') {
              // 输入框间切换：保留用户输入的值，让用户可以修改
              // 不清空字段值，仅标记为无效，阻止焦点切换
              errors.push(`${title == null ? '错误' : title}`);
              validResult = false;
            } else if (validationMode === 'cell-blur') {
              // 输入框失焦：根据字段类型清空值
              if (mxtable && mxtable[rowIndex] && fieldName) {
                // 根据字段类型设置不同的默认值
                if (fieldConfig.fldType === '1' || fieldConfig.fldType === '2') {
                  // 数值类型字段设置为 null
                  mxtable[rowIndex][fieldName] = null;
                } else {
                  // 字符串类型字段设置为空字符串
                  mxtable[rowIndex][fieldName] = '';
                }
              }
              errors.push(`${title == null ? '错误' : title}`);
              validResult = false;
            }
          } else {
            validResult = true;
          }
        } catch (error) {
          errors.push(`校验失败`);
          validResult = false;
        }
      }

      return {
        valid: validResult,
        errors,
        pass: apiPass,
        title: apiTitle,
        clearedField: validResult === false && fieldName ? fieldName : undefined,
        validationMode, // 返回验证模式
      };
    },
    checkRequired(data: any) {
      const { kbxtable } = data;
      const { kbxTableList } = this.djInfo || {};
      if (!kbxTableList) {
        console.warn('djInfo中缺少kbxTableList配置信息');
        return true;
      }
      const kbxTableListKeys = Object.keys(kbxTableList);
      let validResult = true;
      const missingFields: string[] = []; // 收集未填写的必填字段

      if (kbxtable) {
        kbxTableListKeys.forEach((key) => {
          if (!kbxTableList[key].isnull) {
            const fieldName = kbxTableList[key].fieldName;
            if (
              !kbxtable[fieldName] &&
              kbxTableList[key].lx !== 'A' &&
              kbxTableList[key].lx !== 'R' &&
              kbxTableList[key].lx !== 'S' &&
              kbxTableList[key].lx !== 'P'
            ) {
              missingFields.push(kbxTableList[key].fieldTitle);
              validResult = false;
            }
          }
        });

        // 如果有未填写的必填字段,一次性显示
        if (missingFields.length > 0) {
          Modal.warn({
            title: '必填字段错误',
            content: h('div', {
              innerHTML: `以下字段为必填字段，请填写：<br/>${missingFields.map((field) => `<span style="color: red; font-weight: bold;">${field}</span>`).join(`<span style="color: red; font-weight: bold;">、</span>`)}`,
            }),
            okText: '确定',
          });
        }
      }
      return validResult;
    },

    /**
     * 单据设计明细校验
     * @returns Promise<{pass: boolean, result?: ChkDjMxResultVo}>
     */
    async checkDjMx() {
      const result = await aofCheckDjApi({
        gzbh: this.gzbh,
        djlx: this.djlx,
      });

      return result;
    },

    /**
     * 当切换到新的djlx时清理状态
     */
    clearStateForNewDjlx() {
      // 注意：暂时不清理 djInfo，避免组件渲染错误
      // this.djInfo = null;
      this.templateForm = {
        kbxtable: {},
        mxtable: [],
      };
      this.gzbh = '';
      this.danjbh = '';
      this.djSaved = false;
      this.resetForm = false;
      this.lastSavePromise = null;
      this.lastSubmitDataHash = null;
      this.funcinfo = {};
      this.onlyEditField = {};
      this.tempExpContent = {};
      this.firstLoad = true;
      // 注意：不清理 allGzbhList，因为它可能需要跨djlx保持
    },

    /**
     * 强制重新计算所有表达式字段
     * 确保所有包含表达式的字段都是最新计算的结果
     */
    forceRecalculateAllExpressions() {
      const { mxTableList } = this.djInfo || {};
      const { mxtable } = this.templateForm || {};

      if (!mxTableList || !mxtable || mxtable.length === 0) {
        console.log('无明细表数据或配置，跳过表达式重计算');
        return;
      }

      let hasChanges = false;

      // 遍历明细表数据
      mxtable.forEach((row: any, rowIndex: number) => {
        // 遍历所有字段配置，查找包含表达式的字段
        mxTableList.forEach((fieldConfig: any) => {
          if (
            fieldConfig.expContent &&
            fieldConfig.expContent.trim() !== '' &&
            fieldConfig.fieldName
          ) {
            // 重新计算表达式，使用字段配置中的 fldDec 作为精度
            const oldValue = row[fieldConfig.fieldName];
            const newValue = this.calculateExpContent(
              fieldConfig.expContent.toLowerCase(),
              row,
              fieldConfig.fldDec || 2, // 使用字段配置的 fldDec，默认为2
            );

            // 如果计算结果有变化，更新数据
            if (oldValue !== newValue) {
              row[fieldConfig.fieldName] = newValue;
              hasChanges = true;
              console.log(
                `行 ${rowIndex + 1} 字段 ${fieldConfig.fieldName} 表达式重计算: ${oldValue} -> ${newValue} (精度: ${fieldConfig.fldDec || 2})`,
              );
            }
          }
        });
      });

      // 如果有变化，更新模板表单数据
      if (hasChanges) {
        this.setTemplateForm({
          ...this.templateForm,
          mxtable,
        });
        console.log('表达式重计算完成，模板数据已更新');

        // 重新计算汇总项，因为明细数据可能已经改变
        this.calculateAllSummaryItems();
      } else {
        console.log('表达式重计算完成，数据无变化');
      }
    },

    /**
     * 计算所有汇总项（包括表头和表尾的lx为S或P的项目）
     * 确保所有汇总项都被计算，无论是否显示
     */
    calculateAllSummaryItems() {
      const { kbxTableList } = this.djInfo || {};
      const { mxtable } = this.templateForm || {};

      if (!kbxTableList || !mxtable || mxtable.length === 0) {
        return;
      }

      // 计算表头汇总项（lx为S或P的项目）
      const summaryItems = kbxTableList.filter((item: any) => item.lx === 'S' || item.lx === 'P');

      summaryItems.forEach((item: any) => {
        if (item.expContent && item.expContent.trim() !== '') {
          // 如果有表达式，使用表达式计算
          // 对于表头汇总项，使用整个明细表数据进行计算
          const summaryRow = this.createSummaryRowFromMxtable(mxtable);
          const result = this.calculateExpContent(
            item.expContent.toLowerCase(),
            summaryRow,
            item.fldDec || 2, // 使用字段配置的 fldDec，默认为2
          );

          // 根据lx类型设置值
          if (item.lx === 'P') {
            // P类型转换为大写金额
            this.templateForm.kbxtable[item.fieldName] = this.encodeB(result);
          } else {
            // S类型保持数值，应用精度格式化
            this.templateForm.kbxtable[item.fieldName] = this.formatNumberWithPrecision(
              result,
              item.fldDec || 2,
            );
          }
        } else {
          // 如果没有表达式，根据字段名从明细表中求和
          const sum = mxtable.reduce((total: number, row: any) => {
            const value = Number(row[item.fieldName]) || 0;
            return total + value;
          }, 0);

          // 根据lx类型设置值
          if (item.lx === 'P') {
            // P类型转换为大写金额
            this.templateForm.kbxtable[item.fieldName] = this.encodeB(sum);
          } else {
            // S类型保持数值，应用精度格式化
            this.templateForm.kbxtable[item.fieldName] = this.formatNumberWithPrecision(
              sum,
              item.fldDec || 2,
            );
          }
        }
      });

      console.log(
        '汇总项计算完成:',
        summaryItems.map((item: any) => ({
          fieldName: item.fieldName,
          lx: item.lx,
          value: this.templateForm.kbxtable[item.fieldName],
        })),
      );
    },

    /**
     * 从明细表数据创建汇总行，用于表达式计算
     */
    createSummaryRowFromMxtable(mxtable: any[]) {
      const summaryRow: Record<string, any> = {};

      if (!mxtable || mxtable.length === 0) {
        return summaryRow;
      }

      // 获取所有字段名
      const allFields = new Set<string>();
      mxtable.forEach((row: any) => {
        Object.keys(row).forEach((key) => allFields.add(key));
      });

      // 对每个字段进行求和
      allFields.forEach((fieldName) => {
        if (fieldName === '_X_ROW_KEY') return; // 跳过内部字段

        const sum = mxtable.reduce((total: number, row: any) => {
          const value = Number(row[fieldName]) || 0;
          return total + value;
        }, 0);

        summaryRow[fieldName] = sum;
      });

      return summaryRow;
    },

    /**
     * 计算表达式内容
     * @param expContent 表达式内容
     * @param row 数据行
     * @param fldDec 小数位数精度，默认为2
     */
    calculateExpContent(expContent: string, row: Record<string, any>, fldDec: number = 2): number {
      try {
        // 替换变量
        const expression = expContent.replace(/([a-zA-Z]+)/g, (match) => {
          // 检查row中是否存在该变量，存在则返回其值，不存在则返回0
          return row[match] !== undefined ? String(row[match]) : '0';
        });

        // 检查表达式是否有数字后直接跟括号的情况（如"0(..."）并修复
        const safeExpression = expression.replace(/(\d)(\()/g, '$1 * $2');
        const result = new Function(`return ${safeExpression}`)();

        // 将结果转换为数字，然后格式化
        const numResult = Number(result);

        // 检查是否为有效数字且不为无穷大
        if (!isNaN(numResult) && isFinite(numResult)) {
          // 根据 fldDec 指定的精度进行向上取整
          const decimals = Math.max(0, Math.min(fldDec, 10)); // 限制精度范围在 0-10 之间
          // 使用 Math.ceil 进行向上取整，保持指定的小数位数
          const multiplier = Math.pow(10, decimals);
          return Math.ceil(numResult * multiplier) / multiplier;
        } else {
          return 0;
        }
      } catch (error) {
        console.error('表达式计算错误:', error);
        return 0;
      }
    },

    /**
     * 根据指定精度格式化数值
     * @param num 要格式化的数值
     * @param fldDec 小数位数精度，默认为2
     */
    formatNumberWithPrecision(num: number, fldDec: number = 2): number {
      try {
        // 限制精度范围在 0-10 之间
        const decimals = Math.max(0, Math.min(fldDec, 10));
        return Number(num.toFixed(decimals));
      } catch (error) {
        console.error('数值精度格式化错误:', error);
        return num;
      }
    },

    /**
     * 转换为大写金额
     */
    encodeB(num: number): string {
      try {
        return nzhcn.encodeB(num);
      } catch (error) {
        console.error('大写金额转换错误:', error);
        return String(num);
      }
    },
  },
});

// Need to be used outside the setup
export function useTemplateStoreWithOut() {
  return useTemplateStore(store);
}
